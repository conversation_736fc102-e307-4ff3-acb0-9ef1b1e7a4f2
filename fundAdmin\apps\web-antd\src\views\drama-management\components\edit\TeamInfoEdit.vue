<template>
  <div class="settings-container">
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-medium text-gray-900">编辑创作团队</h3>
      <div class="flex items-center space-x-2">
        <Button @click="handleReset">重置</Button>
        <Button type="primary" :loading="saving" @click="handleSave">保存</Button>
      </div>
    </div>

    <Form
      :model="formData"
      :rules="rules"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
      layout="horizontal"
      @finish="handleSave"
    >
      <!-- 出品公司 -->
      <FormItem label="出品公司" name="productionCompany">
        <Select
          v-model:value="formData.productionCompany"
          mode="multiple"
          placeholder="请选择出品公司"
          :loading="brandsLoading"
          show-search
          :filter-option="filterBrandOption"
          allow-clear
        >
          <SelectOption
            v-for="brand in allBrands"
            :key="brand.id"
            :value="brand.companyName"
            :label="brand.companyName"
          >
            {{ brand.companyName }}
          </SelectOption>
        </Select>
      </FormItem>

      <!-- 联合出品公司 -->
      <FormItem label="联合出品公司" name="coProductionCompany">
        <Select
          v-model:value="formData.coProductionCompany"
          mode="multiple"
          placeholder="请选择联合出品公司"
          :loading="brandsLoading"
          show-search
          :filter-option="filterBrandOption"
          allow-clear
        >
          <SelectOption
            v-for="brand in allBrands"
            :key="brand.id"
            :value="brand.companyName"
            :label="brand.companyName"
          >
            {{ brand.companyName }}
          </SelectOption>
        </Select>
      </FormItem>

      <!-- 出品人 -->
      <FormItem label="出品人" name="executiveProducer">
        <Select
          v-model:value="formData.executiveProducer"
          mode="tags"
          placeholder="请选择或输入出品人"
          :loading="actorsLoading"
          show-search
          :filter-option="filterActorOption"
          allow-clear
          :token-separators="[',', '，']"
        >
          <SelectOption
            v-for="actor in allActors"
            :key="actor.id"
            :value="actor.name"
            :label="actor.name"
          >
            {{ actor.name }}
          </SelectOption>
        </Select>
      </FormItem>

      <!-- 联合出品人 -->
      <FormItem label="联合出品人" name="coExecutiveProducer">
        <Select
          v-model:value="formData.coExecutiveProducer"
          mode="tags"
          placeholder="请选择或输入联合出品人"
          :loading="actorsLoading"
          show-search
          :filter-option="filterActorOption"
          allow-clear
          :token-separators="[',', '，']"
        >
          <SelectOption
            v-for="actor in allActors"
            :key="actor.id"
            :value="actor.name"
            :label="actor.name"
          >
            {{ actor.name }}
          </SelectOption>
        </Select>
      </FormItem>

      <!-- 总制片人 -->
      <FormItem label="总制片人" name="chiefProducer">
        <Select
          v-model:value="formData.chiefProducer"
          mode="tags"
          placeholder="请选择或输入总制片人"
          :loading="actorsLoading"
          show-search
          :filter-option="filterActorOption"
          allow-clear
          :token-separators="[',', '，']"
        >
          <SelectOption
            v-for="actor in allActors"
            :key="actor.id"
            :value="actor.name"
            :label="actor.name"
          >
            {{ actor.name }}
          </SelectOption>
        </Select>
      </FormItem>

      <!-- 制片人 -->
      <FormItem label="制片人" name="producer">
        <Select
          v-model:value="formData.producer"
          mode="tags"
          placeholder="请选择或输入制片人"
          :loading="actorsLoading"
          show-search
          :filter-option="filterActorOption"
          allow-clear
          :token-separators="[',', '，']"
        >
          <SelectOption
            v-for="actor in allActors"
            :key="actor.id"
            :value="actor.name"
            :label="actor.name"
          >
            {{ actor.name }}
          </SelectOption>
        </Select>
      </FormItem>

      <!-- 联合制片人 -->
      <FormItem label="联合制片人" name="coProducer">
        <Select
          v-model:value="formData.coProducer"
          mode="tags"
          placeholder="请选择或输入联合制片人"
          :loading="actorsLoading"
          show-search
          :filter-option="filterActorOption"
          allow-clear
          :token-separators="[',', '，']"
        >
          <SelectOption
            v-for="actor in allActors"
            :key="actor.id"
            :value="actor.name"
            :label="actor.name"
          >
            {{ actor.name }}
          </SelectOption>
        </Select>
      </FormItem>

      <!-- 导演 -->
      <FormItem label="导演" name="director">
        <Select
          v-model:value="formData.director"
          mode="tags"
          placeholder="请选择或输入导演"
          :loading="actorsLoading"
          show-search
          :filter-option="filterActorOption"
          allow-clear
          :token-separators="[',', '，']"
        >
          <SelectOption
            v-for="actor in allActors"
            :key="actor.id"
            :value="actor.name"
            :label="actor.name"
          >
            {{ actor.name }}
          </SelectOption>
        </Select>
      </FormItem>

      <!-- 编剧 -->
      <FormItem label="编剧" name="scriptwriter">
        <Select
          v-model:value="formData.scriptwriter"
          mode="tags"
          placeholder="请选择或输入编剧"
          :loading="actorsLoading"
          show-search
          :filter-option="filterActorOption"
          allow-clear
          :token-separators="[',', '，']"
        >
          <SelectOption
            v-for="actor in allActors"
            :key="actor.id"
            :value="actor.name"
            :label="actor.name"
          >
            {{ actor.name }}
          </SelectOption>
        </Select>
      </FormItem>

      <!-- 监制 -->
      <FormItem label="监制" name="supervisor">
        <Select
          v-model:value="formData.supervisor"
          mode="tags"
          placeholder="请选择或输入监制"
          :loading="actorsLoading"
          show-search
          :filter-option="filterActorOption"
          allow-clear
          :token-separators="[',', '，']"
        >
          <SelectOption
            v-for="actor in allActors"
            :key="actor.id"
            :value="actor.name"
            :label="actor.name"
          >
            {{ actor.name }}
          </SelectOption>
        </Select>
      </FormItem>

      <!-- 统筹 -->
      <FormItem label="统筹" name="coordinator">
        <Select
          v-model:value="formData.coordinator"
          mode="tags"
          placeholder="请选择或输入统筹"
          :loading="actorsLoading"
          show-search
          :filter-option="filterActorOption"
          allow-clear
          :token-separators="[',', '，']"
        >
          <SelectOption
            v-for="actor in allActors"
            :key="actor.id"
            :value="actor.name"
            :label="actor.name"
          >
            {{ actor.name }}
          </SelectOption>
        </Select>
      </FormItem>

      <!-- 主演阵容 -->
      <FormItem label="主演阵容" name="cast">
        <Select
          v-model:value="formData.cast"
          mode="tags"
          placeholder="请选择或输入演员姓名，支持多个演员"
          :loading="actorsLoading"
          show-search
          :filter-option="filterActorOption"
          :token-separators="[',', '，']"
        >
          <SelectOption
            v-for="actor in allActors"
            :key="actor.id"
            :value="actor.name"
            :label="actor.name"
          >
            {{ actor.name }}
          </SelectOption>
        </Select>
      </FormItem>
    </Form>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed, onMounted } from 'vue';
import {
  Form,
  FormItem,
  Input,
  Select,
  SelectOption,
  Button,
  message,
} from 'ant-design-vue';

import {
  updateDrama,
  getBrandsForSelector,
  getActorsForSelector,
  type DramaManagementApi
} from '#/api/drama-management';

interface Props {
  drama: DramaManagementApi.Drama;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  saveSuccess: [];
  dataChanged: [hasChanges: boolean];
}>();

const saving = ref(false);
const brandsLoading = ref(false);
const actorsLoading = ref(false);

// 选择器数据
const allBrands = ref<any[]>([]);
const allActors = ref<any[]>([]);

// 表单数据 - 根据drama_production_team表结构
const formData = reactive({
  // 公司信息（绑定到厂牌，支持多选）
  productionCompany: parseCompanyData(props.drama.productionTeam?.productionCompany) || [],
  coProductionCompany: parseCompanyData(props.drama.productionTeam?.coProductionCompany) || [],

  // 人员信息（绑定到演员或自由输入，支持多个）
  executiveProducer: parsePersonData(props.drama.productionTeam?.executiveProducer) || [],
  coExecutiveProducer: parsePersonData(props.drama.productionTeam?.coExecutiveProducer) || [],
  chiefProducer: parsePersonData(props.drama.productionTeam?.chiefProducer) || [],
  producer: parsePersonData(props.drama.productionTeam?.producer) || [],
  coProducer: parsePersonData(props.drama.productionTeam?.coProducer) || [],
  director: parsePersonData(props.drama.productionTeam?.director) || [],
  scriptwriter: parsePersonData(props.drama.productionTeam?.scriptwriter) || [],
  supervisor: parsePersonData(props.drama.productionTeam?.supervisor) || [],
  coordinator: parsePersonData(props.drama.productionTeam?.coordinator) || [],

  // 主演阵容（绑定到演员）
  cast: props.drama.cast || [],
});

// 解析公司数据（支持ID数组和字符串格式，返回公司名数组用于前端显示）
function parseCompanyData(data: string | number[] | null | undefined): string[] {
  if (!data) return [];

  // 如果是数组格式（ID数组），转换为公司名数组
  if (Array.isArray(data)) {
    return data.map(id => {
      const brand = allBrands.value.find(b => b.id === id);
      return brand ? brand.companyName : `未知公司(ID:${id})`;
    });
  }

  if (typeof data === 'string') {
    try {
      // 尝试解析JSON格式的ID数组
      const parsed = JSON.parse(data);
      if (Array.isArray(parsed) && parsed.every(item => typeof item === 'number')) {
        return parsed.map(id => {
          const brand = allBrands.value.find(b => b.id === id);
          return brand ? brand.companyName : `未知公司(ID:${id})`;
        });
      }
    } catch {
      // JSON解析失败，按逗号分隔的字符串处理（向后兼容）
      return data.split(',').map(item => item.trim()).filter(Boolean);
    }
  }

  return [];
}

// 将公司名数组转换为ID数组
function convertCompanyNamesToIds(names: string[]): number[] {
  return names.map(name => {
    const brand = allBrands.value.find(b => b.companyName === name);
    return brand ? brand.id : null;
  }).filter((id): id is number => id !== null);
}

// 将公司名数组转换为ID数组的JSON字符串（用于保存到数据库）
function convertCompanyNamesToIdsJson(names: string[]): string | null {
  if (!names || names.length === 0) return null;
  const ids = convertCompanyNamesToIds(names);
  return ids.length > 0 ? JSON.stringify(ids) : null;
}

// 解析人员数据（支持ID数组和字符串格式，返回姓名数组用于前端显示）
function parsePersonData(data: string | number[] | null | undefined): string[] {
  if (!data) return [];

  // 如果是数组格式（ID数组），转换为姓名数组
  if (Array.isArray(data)) {
    return data.map(id => {
      const actor = allActors.value.find(a => a.id === id);
      return actor ? actor.name : `未知演员(ID:${id})`;
    });
  }

  if (typeof data === 'string') {
    try {
      // 尝试解析JSON格式的ID数组
      const parsed = JSON.parse(data);
      if (Array.isArray(parsed) && parsed.every(item => typeof item === 'number')) {
        return parsed.map(id => {
          const actor = allActors.value.find(a => a.id === id);
          return actor ? actor.name : `未知演员(ID:${id})`;
        });
      }
    } catch {
      // JSON解析失败，按逗号分隔的字符串处理（向后兼容）
      return data.split(',').map(item => item.trim()).filter(Boolean);
    }
  }

  return [];
}

// 将姓名数组转换为ID数组
function convertNamesToIds(names: string[]): number[] {
  return names.map(name => {
    const actor = allActors.value.find(a => a.name === name);
    return actor ? actor.id : null;
  }).filter((id): id is number => id !== null);
}

// 表单验证规则
const rules = {
  // 团队信息都是可选的，不设置必填验证
};

// 加载选择器数据
const loadSelectorData = async () => {
  try {
    // 加载厂牌数据
    brandsLoading.value = true;
    const brandsRes = await getBrandsForSelector();
    allBrands.value = brandsRes.result || [];

    // 加载演员数据
    actorsLoading.value = true;
    const actorsRes = await getActorsForSelector();
    allActors.value = actorsRes.result || [];
  } catch (error) {
    console.error('加载选择器数据失败:', error);
    message.error('加载选择器数据失败');
  } finally {
    brandsLoading.value = false;
    actorsLoading.value = false;
  }
};

// 厂牌搜索过滤
const filterBrandOption = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase());
};

// 演员搜索过滤
const filterActorOption = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase());
};



// 检查是否有数据更改
const hasDataChanged = computed(() => {
  const original = props.drama;
  const originalTeam = original.productionTeam || {};

  return (
    JSON.stringify(formData.productionCompany) !== JSON.stringify(parseCompanyData(originalTeam.productionCompany)) ||
    JSON.stringify(formData.coProductionCompany) !== JSON.stringify(parseCompanyData(originalTeam.coProductionCompany)) ||
    JSON.stringify(formData.executiveProducer) !== JSON.stringify(parsePersonData(originalTeam.executiveProducer)) ||
    JSON.stringify(formData.coExecutiveProducer) !== JSON.stringify(parsePersonData(originalTeam.coExecutiveProducer)) ||
    JSON.stringify(formData.chiefProducer) !== JSON.stringify(parsePersonData(originalTeam.chiefProducer)) ||
    JSON.stringify(formData.producer) !== JSON.stringify(parsePersonData(originalTeam.producer)) ||
    JSON.stringify(formData.coProducer) !== JSON.stringify(parsePersonData(originalTeam.coProducer)) ||
    JSON.stringify(formData.director) !== JSON.stringify(parsePersonData(originalTeam.director)) ||
    JSON.stringify(formData.scriptwriter) !== JSON.stringify(parsePersonData(originalTeam.scriptwriter)) ||
    JSON.stringify(formData.supervisor) !== JSON.stringify(parsePersonData(originalTeam.supervisor)) ||
    JSON.stringify(formData.coordinator) !== JSON.stringify(parsePersonData(originalTeam.coordinator)) ||
    JSON.stringify(formData.cast) !== JSON.stringify(parsePersonData(original.cast))
  );
});

// 监听数据变化并通知父组件
watch(hasDataChanged, (changed) => {
  emit('dataChanged', changed);
}, { immediate: true });



// 将姓名数组转换为ID数组的JSON字符串（用于保存到数据库）
const convertNamesToIdsJson = (names: string[]): string | null => {
  if (!names || names.length === 0) return null;
  const ids = convertNamesToIds(names);
  return ids.length > 0 ? JSON.stringify(ids) : null;
};

// 保存
async function handleSave() {
  try {
    saving.value = true;

    // 构建制作团队数据
    const productionTeam = {
      productionCompany: convertCompanyNamesToIdsJson(formData.productionCompany),
      coProductionCompany: convertCompanyNamesToIdsJson(formData.coProductionCompany),
      executiveProducer: convertNamesToIdsJson(formData.executiveProducer),
      coExecutiveProducer: convertNamesToIdsJson(formData.coExecutiveProducer),
      chiefProducer: convertNamesToIdsJson(formData.chiefProducer),
      producer: convertNamesToIdsJson(formData.producer),
      coProducer: convertNamesToIdsJson(formData.coProducer),
      director: convertNamesToIdsJson(formData.director),
      scriptwriter: convertNamesToIdsJson(formData.scriptwriter),
      supervisor: convertNamesToIdsJson(formData.supervisor),
      coordinator: convertNamesToIdsJson(formData.coordinator),
    };

    // 主演阵容也转换为ID数组，空数组时传递空数组而不是undefined
    const castIds = formData.cast && formData.cast.length > 0 ? convertNamesToIds(formData.cast) : [];

    const updateData = {
      id: props.drama.id,
      productionTeam,
      cast: castIds,
    };

    await updateDrama(props.drama.id, updateData);
    message.success('创作团队信息保存成功');
    emit('saveSuccess');
  } catch (error: any) {
    message.error(error.message || '保存失败');
  } finally {
    saving.value = false;
  }
}

// 重置表单数据
const resetFormData = (drama: DramaManagementApi.Drama) => {
  const team = drama.productionTeam || {};
  Object.assign(formData, {
    productionCompany: parseCompanyData(team.productionCompany),
    coProductionCompany: parseCompanyData(team.coProductionCompany),
    executiveProducer: parsePersonData(team.executiveProducer),
    coExecutiveProducer: parsePersonData(team.coExecutiveProducer),
    chiefProducer: parsePersonData(team.chiefProducer),
    producer: parsePersonData(team.producer),
    coProducer: parsePersonData(team.coProducer),
    director: parsePersonData(team.director),
    scriptwriter: parsePersonData(team.scriptwriter),
    supervisor: parsePersonData(team.supervisor),
    coordinator: parsePersonData(team.coordinator),
    cast: parsePersonData(drama.cast), // cast字段也需要解析ID数组
  });
};

// 重置
function handleReset() {
  resetFormData(props.drama);
  message.info('已重置为原始数据');
}

// 监听props变化
watch(() => props.drama, (newDrama) => {
  resetFormData(newDrama);
}, { deep: true });

// 组件挂载时加载数据
onMounted(async () => {
  await loadSelectorData();
  // 数据加载完成后重置表单，确保ID映射正确
  resetFormData(props.drama);
});
</script>

<style scoped>
/* 禁用输入框样式 */
:deep(.input-disabled) {
  color: #1f2937 !important;
  background-color: #f9fafb;
  border-color: #e5e7eb;
}
</style>
