<template>
  <div class="admin-card">
    <a-form
      ref="formRef"
      :model="formState"
      :rules="rules"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
      class="settings-form"
      layout="horizontal"
    >
      <!-- 基金基本信息 -->
      <a-form-item label="基金代码" name="code" class="form-item-visible">
        <a-input
          v-model:value="formState.code"
          placeholder="请输入基金代码"
        />
      </a-form-item>

      <a-form-item label="基金名称" name="title" class="form-item-visible">
        <a-input
          v-model:value="formState.title"
          placeholder="请输入基金名称"
        />
      </a-form-item>

      <a-form-item label="基金描述" name="description" class="form-item-visible">
        <a-textarea
          v-model:value="formState.description"
          :rows="3"
          placeholder="请输入基金描述"
        />
      </a-form-item>

      <a-form-item label="基金类型" name="type" class="form-item-visible">
        <a-select
          v-model:value="formState.type"
          placeholder="请选择基金类型"
        >
          <a-select-option value="equity">股权型</a-select-option>
          <a-select-option value="debt">债权型</a-select-option>
          <a-select-option value="mixed">混合型</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="风险等级" name="risk" class="form-item-visible">
        <a-select
          v-model:value="formState.risk"
          placeholder="请选择风险等级"
        >
          <a-select-option value="R1">R1（低风险）</a-select-option>
          <a-select-option value="R2">R2（中低风险）</a-select-option>
          <a-select-option value="R3">R3（中等风险）</a-select-option>
          <a-select-option value="R4">R4（中高风险）</a-select-option>
          <a-select-option value="R5">R5（高风险）</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="最低起投金额" name="minInvestment" class="form-item-visible">
        <a-input-number
          v-model:value="formState.minInvestment"
          :min="0"
          :step="1000"
          :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
          :parser="value => value.replace(/¥\s?|(,*)/g, '')"
          placeholder="请输入最低起投金额"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="封闭期类型" name="period" class="form-item-visible">
        <a-select
          v-model:value="formState.period"
          placeholder="请选择封闭期类型"
        >
          <a-select-option value="short">≤1年</a-select-option>
          <a-select-option value="medium">1-3年</a-select-option>
          <a-select-option value="long">>3年</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="目标募集规模" name="targetSize" class="form-item-visible">
        <a-input-number
          v-model:value="formState.targetSize"
          :min="0"
          :step="1000000"
          :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
          :parser="value => value.replace(/¥\s?|(,*)/g, '')"
          placeholder="请输入目标募集规模"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="已募集金额" name="raisedAmount" class="form-item-visible">
        <a-input-number
          v-model:value="formState.raisedAmount"
          :min="0"
          :step="1000000"
          :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
          :parser="value => value.replace(/¥\s?|(,*)/g, '')"
          placeholder="请输入已募集金额"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="预期年化收益率" name="expectedReturn" class="form-item-visible">
        <a-input
          v-model:value="formState.expectedReturn"
          placeholder="请输入预期年化收益率，如：8-12%"
        />
      </a-form-item>

      <a-form-item label="最低持有期" name="minHoldingPeriod" class="form-item-visible">
        <a-input-number
          v-model:value="formState.minHoldingPeriod"
          :min="0"
          :step="1"
          placeholder="请输入最低持有期（月）"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="基金管理人" name="manager" class="form-item-visible">
        <a-input
          v-model:value="formState.manager"
          placeholder="请输入基金管理人"
        />
      </a-form-item>

      <a-form-item label="基金托管人" name="trustee" class="form-item-visible">
        <a-input
          v-model:value="formState.trustee"
          placeholder="请输入基金托管人"
        />
      </a-form-item>

      <a-form-item label="成立日期" name="establishDate" class="form-item-visible">
        <a-date-picker
          v-model:value="formState.establishDate"
          placeholder="请选择成立日期"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="到期日期" name="exitDate" class="form-item-visible">
        <a-date-picker
          v-model:value="formState.exitDate"
          placeholder="请选择到期日期"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="投资策略" name="investmentStrategy" class="form-item-visible">
        <a-textarea
          v-model:value="formState.investmentStrategy"
          :rows="3"
          placeholder="请输入投资策略"
        />
      </a-form-item>

      <a-form-item label="赎回政策" name="redemptionPolicy" class="form-item-visible">
        <a-textarea
          v-model:value="formState.redemptionPolicy"
          :rows="3"
          placeholder="请输入赎回政策"
        />
      </a-form-item>

      <a-form-item label="风险描述" name="riskDescription" class="form-item-visible">
        <a-textarea
          v-model:value="formState.riskDescription"
          :rows="3"
          placeholder="请输入风险描述"
        />
      </a-form-item>

      <a-form-item label="发布状态" name="isPublished" class="form-item-visible">
        <a-switch
          v-model:checked="formState.isPublished"
          checked-children="已发布"
          un-checked-children="草稿"
        />
      </a-form-item>

      <!-- 保存按钮 -->
      <div class="flex justify-end mt-6">
        <a-button type="primary" :loading="saving" @click="handleSave">
          保存基本信息
        </a-button>
      </div>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue';
import { 
  Form as AForm, 
  FormItem as AFormItem, 
  Input as AInput, 
  Textarea as ATextarea, 
  Select as ASelect,
  SelectOption as ASelectOption,
  InputNumber as AInputNumber,
  DatePicker as ADatePicker,
  Switch as ASwitch,
  Button as AButton,
  message
} from 'ant-design-vue';
import dayjs from 'dayjs';
import { updateFund } from '#/api/fund-management';

interface Props {
  fund: any;
}

const props = defineProps<Props>();

// 定义emits
const emit = defineEmits<{
  saveSuccess: [message?: string];
}>();

// 表单引用
const formRef = ref();
const saving = ref(false);

// 表单状态
const formState = reactive({
  code: '',
  title: '',
  description: '',
  type: '',
  risk: '',
  minInvestment: 0,
  period: '',
  targetSize: 0,
  raisedAmount: 0,
  expectedReturn: '',
  minHoldingPeriod: 0,
  manager: '',
  trustee: '',
  establishDate: null,
  exitDate: null,
  investmentStrategy: '',
  redemptionPolicy: '',
  riskDescription: '',
  isPublished: false,
});

// 表单验证规则
const rules = {
  code: [
    { required: true, message: '请输入基金代码', trigger: 'blur' },
    { min: 3, max: 20, message: '基金代码长度应在3-20个字符之间', trigger: 'blur' }
  ],
  title: [
    { required: true, message: '请输入基金名称', trigger: 'blur' },
    { min: 2, max: 100, message: '基金名称长度应在2-100个字符之间', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择基金类型', trigger: 'change' }
  ],
  risk: [
    { required: true, message: '请选择风险等级', trigger: 'change' }
  ],
  minInvestment: [
    { required: true, message: '请输入最低起投金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '最低起投金额不能小于0', trigger: 'blur' }
  ],
  period: [
    { required: true, message: '请选择封闭期类型', trigger: 'change' }
  ],
  targetSize: [
    { required: true, message: '请输入目标募集规模', trigger: 'blur' },
    { type: 'number', min: 0, message: '目标募集规模不能小于0', trigger: 'blur' }
  ],
  minHoldingPeriod: [
    { required: true, message: '请输入最低持有期', trigger: 'blur' },
    { type: 'number', min: 0, message: '最低持有期不能小于0', trigger: 'blur' }
  ]
};

// 初始化表单数据
const initFormData = () => {
  if (props.fund) {
    Object.assign(formState, {
      code: props.fund.code || '',
      title: props.fund.title || '',
      description: props.fund.description || '',
      type: props.fund.type || '',
      risk: props.fund.risk || '',
      minInvestment: props.fund.min_investment || 0,
      period: props.fund.period || '',
      targetSize: props.fund.target_size || 0,
      raisedAmount: props.fund.raised_amount || 0,
      expectedReturn: props.fund.expected_return || '',
      minHoldingPeriod: props.fund.min_holding_period || 0,
      manager: props.fund.manager || '',
      trustee: props.fund.trustee || '',
      establishDate: props.fund.establish_date ? dayjs(props.fund.establish_date) : null,
      exitDate: props.fund.exit_date ? dayjs(props.fund.exit_date) : null,
      investmentStrategy: props.fund.investment_strategy || '',
      redemptionPolicy: props.fund.redemption_policy || '',
      riskDescription: props.fund.risk_description || '',
      isPublished: props.fund.is_published === 1,
    });
  }
};

// 保存基本信息
const handleSave = async () => {
  try {
    await formRef.value.validate();

    saving.value = true;

    // 准备提交数据 - 转换字段名为后端期望的下划线命名
    const submitData = {
      code: formState.code,
      title: formState.title,
      description: formState.description,
      type: formState.type,
      risk: formState.risk,
      min_investment: formState.minInvestment,
      period: formState.period,
      target_size: formState.targetSize,
      raised_amount: formState.raisedAmount,
      expected_return: formState.expectedReturn,
      min_holding_period: formState.minHoldingPeriod,
      manager: formState.manager,
      trustee: formState.trustee,
      establish_date: formState.establishDate ? formState.establishDate.format('YYYY-MM-DD') : null,
      exit_date: formState.exitDate ? formState.exitDate.format('YYYY-MM-DD') : null,
      investment_strategy: formState.investmentStrategy,
      redemption_policy: formState.redemptionPolicy,
      risk_description: formState.riskDescription,
      is_published: formState.isPublished ? 1 : 0,
    };

    await updateFund(props.fund.id, submitData);

    emit('saveSuccess', '基本信息保存成功');
  } catch (error: any) {
    console.error('保存基本信息失败:', error);
    if (error.errorFields) {
      // 表单验证错误
      message.error('请检查表单填写是否正确');
    } else {
      // API错误
      message.error(error.message || '保存失败，请重试');
    }
  } finally {
    saving.value = false;
  }
};

// 监听fund变化，重新初始化表单
watch(() => props.fund, () => {
  initFormData();
}, { immediate: true, deep: true });
</script>

<style scoped>
.admin-card {
  @apply bg-white p-6 rounded-lg shadow-sm;
}

.settings-form {
  @apply max-w-none;
}

.form-item-visible {
  @apply mb-4;
}

:deep(.ant-form-item-label) {
  @apply font-medium;
}
</style>
