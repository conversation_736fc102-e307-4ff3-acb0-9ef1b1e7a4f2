<script lang="ts" setup>
import type { FundManagementApi } from '#/api/fund-management';

import { computed, ref } from 'vue';

import { useVbenDrawer, useVbenForm } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import {
  createFund,
  getFundDetail,
  updateFund,
} from '#/api/fund-management';

import { fundTypeOptions, periodOptions, riskLevelOptions } from '../data';

interface Props {
  onSuccess?: () => void;
}

const props = withDefaults(defineProps<Props>(), {});

const emits = defineEmits<{
  success: [];
}>();

const id = ref();
const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const values = await formApi.getValues();
    drawerApi.lock();

    // 转换数据格式 - 将表单字段名转换为后端期望的下划线命名
    const submitData = {
      code: values.code,
      title: values.title,
      description: values.description,
      type: values.type,
      risk: values.risk,
      min_investment: values.min_investment,
      period: values.period,
      target_size: values.target_size,
      raised_amount: values.raised_amount,
      expected_return: values.expected_return,
      min_holding_period: values.min_holding_period,
      manager: values.manager,
      trustee: values.trustee,
      establish_date: values.establish_date,
      exit_date: values.exit_date,
      investment_strategy: values.investment_strategy,
      redemption_policy: values.redemption_policy,
      risk_description: values.risk_description,
      is_published: values.is_published ? 1 : 0,
    };

    (id.value ? updateFund(id.value, submitData) : createFund(submitData))
      .then(() => {
        emits('success');
        drawerApi.close();
        message.success(id.value ? '更新基金成功' : '创建基金成功');
        // 通知父组件刷新数据
        if (props.onSuccess) {
          props.onSuccess();
        }
      })
      .catch(() => {
        drawerApi.unlock();
      });
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<FundManagementApi.Fund>();
      formApi.resetForm();
      if (data && data.id) {
        // 编辑模式
        id.value = data.id;
        // 获取完整的基金详情
        try {
          const fundDetail = await getFundDetail(data.id);
          formApi.setValues({
            ...fundDetail,
            is_published: Boolean(fundDetail.is_published),
          });
        } catch (error: any) {
          console.error('获取基金详情失败:', error);
          message.error(error.message || '获取基金详情失败');
          // 如果获取详情失败，使用传入的基本数据
          formApi.setValues({
            ...data,
            is_published: Boolean(data.is_published),
          });
        }
      } else {
        // 创建模式
        id.value = undefined;
        formApi.setValues({
          is_published: false,
          raised_amount: 0,
          min_investment: 0,
          target_size: 0,
          expected_return: 0,
          min_holding_period: 0,
        });
      }
    }
  },
});

const isEdit = computed(() => Boolean(id.value));
const isView = computed(() => false); // 暂时不支持查看模式
const isCreate = computed(() => !id.value);

const [Form, formApi] = useVbenForm({
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入基金代码',
      },
      fieldName: 'code',
      label: '基金代码',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入基金名称',
      },
      fieldName: 'title',
      label: '基金名称',
      rules: 'required',
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入基金简介',
        rows: 3,
      },
      fieldName: 'description',
      label: '基金简介',
    },
    {
      component: 'Select',
      componentProps: {
        options: fundTypeOptions,
        placeholder: '请选择基金类型',
      },
      fieldName: 'type',
      label: '基金类型',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        options: riskLevelOptions,
        placeholder: '请选择风险等级',
      },
      fieldName: 'risk',
      label: '风险等级',
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入最低起投金额',
        min: 0,
        formatter: (value: number) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
        parser: (value: string) => value.replace(/\$\s?|(,*)/g, ''),
      },
      fieldName: 'min_investment',
      label: '最低起投金额(元)',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        options: periodOptions,
        placeholder: '请选择封闭期类型',

      },
      fieldName: 'period',
      label: '封闭期类型',
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入目标募集规模',
        min: 0,
        formatter: (value: number) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
        parser: (value: string) => value.replace(/\$\s?|(,*)/g, ''),

      },
      fieldName: 'target_size',
      label: '目标募集规模(元)',
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入已募集金额',
        min: 0,
        formatter: (value: number) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
        parser: (value: string) => value.replace(/\$\s?|(,*)/g, ''),
      },
      fieldName: 'raised_amount',
      label: '已募集金额(元)',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入预期年化收益率，如：15-20%',
      },
      fieldName: 'expected_return',
      label: '预期年化收益率',
    },
    {
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入最低持有期',
        min: 0,
      },
      fieldName: 'min_holding_period',
      label: '最低持有期(月)',
      rules: 'required',
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入风险描述',
        rows: 3,
      },
      fieldName: 'risk_description',
      label: '风险描述',
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择成立日期',
        format: 'YYYY-MM-DD',
      },
      fieldName: 'establish_date',
      label: '成立日期',
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择到期日期',
        format: 'YYYY-MM-DD',
      },
      fieldName: 'exit_date',
      label: '到期日期',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入基金管理人',
      },
      fieldName: 'manager',
      label: '基金管理人',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入基金托管人',
      },
      fieldName: 'trustee',
      label: '基金托管人',
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入赎回政策',
        rows: 3,
      },
      fieldName: 'redemption_policy',
      label: '赎回政策',
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入投资策略',
        rows: 3,
      },
      fieldName: 'investment_strategy',
      label: '投资策略',
    },
    {
      component: 'Switch',
      componentProps: {
        checkedChildren: '已发布',
        unCheckedChildren: '草稿',
      },
      fieldName: 'is_published',
      label: '发布状态',
    },
  ],
});


</script>

<template>
  <Drawer
    :title="isView ? '查看基金' : isEdit ? '编辑基金' : '创建基金'"
    :width="800"
    :confirm-text="isView ? undefined : isEdit ? '更新' : '创建'"
    :show-confirm="!isView"
  >
    <Form />
  </Drawer>
</template>
