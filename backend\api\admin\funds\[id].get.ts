import { query } from '~/utils/database';
import { logger, logAuditAction, getClientIP } from '~/utils/logger';

/**
 * 获取单个基金详情接口（管理员）
 * GET /api/admin/funds/[id]
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: '未授权访问'
      });
    }

    // 临时跳过权限检查，直接允许访问
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法查看基金详情'
    //   });
    // }

    // 获取基金ID
    const fundId = getRouterParam(event, 'id');
    if (!fundId) {
      throw createError({
        statusCode: 400,
        statusMessage: '基金ID不能为空'
      });
    }

    // 查询基金基本信息
    const funds = await query(
      `SELECT id, code, title, description, type, risk, min_investment, period,
              target_size, raised_amount, expected_return, min_holding_period,
              risk_description, establish_date, exit_date, manager, trustee,
              redemption_policy, investment_strategy, is_published,
              created_at, updated_at
       FROM funds 
       WHERE id = ?`,
      [fundId]
    );

    if (funds.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '基金不存在'
      });
    }

    const fund = funds[0];

    // 获取关联数据 - 基于真实数据库字段结构
    const [highlights, documents, faqs, timelines, fees, performances, usagePlans, usagePlanDescriptions, successCases] = await Promise.all([
      // 1. 基金亮点表 (fund_highlights)
      // 字段: id, fund_id, content, sort_order, created_at
      query('SELECT id, fund_id, content, sort_order, created_at FROM fund_highlights WHERE fund_id = ? ORDER BY sort_order, id', [fund.id]),

      // 2. 基金文档表 (fund_documents)
      // 字段: id, fund_id, name, file_url, file_type, file_size, created_at
      query('SELECT id, fund_id, name, file_url, file_type, file_size, created_at FROM fund_documents WHERE fund_id = ? ORDER BY id', [fund.id]),

      // 3. 常见问题表 (fund_faqs)
      // 字段: id, fund_id, question, answer, sort_order, created_at
      query('SELECT id, fund_id, question, answer, sort_order, created_at FROM fund_faqs WHERE fund_id = ? ORDER BY sort_order, id', [fund.id]),

      // 4. 时间线表 (fund_timelines)
      // 字段: id, fund_id, stage, date, status, sort_order, created_at
      query('SELECT id, fund_id, stage, date, status, sort_order, created_at FROM fund_timelines WHERE fund_id = ? ORDER BY sort_order, id', [fund.id]),

      // 5. 费用结构表 (fund_fees)
      // 字段: id, fund_id, name, value, sort_order, created_at
      query('SELECT id, fund_id, name, value, sort_order, created_at FROM fund_fees WHERE fund_id = ? ORDER BY sort_order, id', [fund.id]),

      // 6. 历史业绩表 (fund_performances)
      // 字段: id, fund_id, year, return, average, created_at
      query('SELECT id, fund_id, year, `return`, average, created_at FROM fund_performances WHERE fund_id = ? ORDER BY year DESC', [fund.id]),

      // 7. 资金使用计划表 (fund_usage_plans)
      // 字段: id, fund_id, purpose, amount, percentage, description, sort_order, created_at, updated_at
      query('SELECT id, fund_id, purpose, amount, percentage, description, sort_order, created_at, updated_at FROM fund_usage_plans WHERE fund_id = ? ORDER BY sort_order, id', [fund.id]),

      // 8. 资金使用计划描述表 (fund_usage_plan_descriptions)
      // 字段: id, fund_id, description, created_at, updated_at
      query('SELECT id, fund_id, description, created_at, updated_at FROM fund_usage_plan_descriptions WHERE fund_id = ? ORDER BY id', [fund.id]),

      // 9. 成功案例表 (fund_success_cases)
      // 字段: id, fund_id, title, description, return_rate, investment_amount, recovery_period, sort_order, created_at, updated_at
      query('SELECT id, fund_id, title, description, return_rate, investment_amount, recovery_period, sort_order, created_at, updated_at FROM fund_success_cases WHERE fund_id = ? ORDER BY sort_order, id', [fund.id])
    ]);

    // 格式化基金详情数据 - 保持与数据库字段名一致（下划线命名）
    const formattedFund = {
      // 基金主表数据 (funds)
      id: fund.id,
      code: fund.code,
      title: fund.title,
      description: fund.description,
      type: fund.type,
      risk: fund.risk,
      min_investment: fund.min_investment,
      period: fund.period,
      target_size: fund.target_size,
      raised_amount: fund.raised_amount,
      expected_return: fund.expected_return,
      min_holding_period: fund.min_holding_period,
      risk_description: fund.risk_description,
      establish_date: fund.establish_date,
      exit_date: fund.exit_date,
      manager: fund.manager,
      trustee: fund.trustee,
      redemption_policy: fund.redemption_policy,
      investment_strategy: fund.investment_strategy,
      is_published: fund.is_published,
      created_at: fund.created_at,
      updated_at: fund.updated_at,

      // 关联数据 - 严格按照数据库字段映射

      // 1. 基金亮点 (fund_highlights)
      highlights: highlights.map((h: any) => ({
        id: h.id,
        fundId: h.fund_id,
        content: h.content,
        sortOrder: h.sort_order,
        createdAt: h.created_at
      })),

      // 2. 基金文档 (fund_documents)
      documents: documents.map((d: any) => ({
        id: d.id,
        fundId: d.fund_id,
        name: d.name,
        fileUrl: d.file_url,
        fileType: d.file_type,
        fileSize: d.file_size,
        createdAt: d.created_at
      })),

      // 3. 常见问题 (fund_faqs)
      faqs: faqs.map((f: any) => ({
        id: f.id,
        fundId: f.fund_id,
        question: f.question,
        answer: f.answer,
        sortOrder: f.sort_order,
        createdAt: f.created_at
      })),

      // 4. 时间线 (fund_timelines)
      timelines: timelines.map((t: any) => ({
        id: t.id,
        fundId: t.fund_id,
        stage: t.stage,
        date: t.date,
        status: t.status,
        sortOrder: t.sort_order,
        createdAt: t.created_at
      })),

      // 5. 费用结构 (fund_fees)
      fees: fees.map((f: any) => ({
        id: f.id,
        fundId: f.fund_id,
        name: f.name,
        value: f.value,
        sortOrder: f.sort_order,
        createdAt: f.created_at
      })),

      // 6. 历史业绩 (fund_performances)
      performances: performances.map((p: any) => ({
        id: p.id,
        fundId: p.fund_id,
        year: p.year,
        return: p.return,
        average: p.average,
        createdAt: p.created_at
      })),

      // 7. 资金使用计划 (fund_usage_plans)
      usagePlans: usagePlans.map((u: any) => ({
        id: u.id,
        fundId: u.fund_id,
        purpose: u.purpose || u.description, // 优先使用purpose字段，fallback到description
        amount: u.amount,
        percentage: u.percentage,
        description: u.description,
        sortOrder: u.sort_order,
        createdAt: u.created_at,
        updatedAt: u.updated_at
      })),

      // 8. 资金使用计划描述 (fund_usage_plan_descriptions)
      usagePlanDescriptions: usagePlanDescriptions.map((d: any) => ({
        id: d.id,
        fundId: d.fund_id,
        description: d.description,
        createdAt: d.created_at,
        updatedAt: d.updated_at
      })),

      // 9. 成功案例 (fund_success_cases)
      successCases: successCases.map((s: any) => ({
        id: s.id,
        fundId: s.fund_id,
        title: s.title,
        description: s.description,
        return: s.return_rate, // 映射为前端期望的字段名
        investment: s.investment_amount, // 映射为前端期望的字段名
        recoveryPeriod: s.recovery_period,
        returnRate: s.return_rate, // 保留原字段名用于兼容
        investmentAmount: s.investment_amount, // 保留原字段名用于兼容
        sortOrder: s.sort_order,
        createdAt: s.created_at,
        updatedAt: s.updated_at
      }))
    };

    return {
      success: true,
      data: formattedFund
    };

  } catch (error: any) {
    logger.error('获取基金详情失败', {
      error: error.message,
      fundId: getRouterParam(event, 'id'),
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error;
    }

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    });
  }
});
