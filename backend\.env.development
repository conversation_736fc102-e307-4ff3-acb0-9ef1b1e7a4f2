# 剧投投后端服务 - 本地开发环境配置

# 服务器配置
PORT=3001
NODE_ENV=development

# 数据库配置 - 本地开发环境
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USER=root
DB_PASSWORD=123456
DB_NAME=mengtu

# JWT配置 - 开发环境密钥
JWT_SECRET=mengtutv_jwt_secret_development_key_2024
JWT_EXPIRES_IN=24h
JWT_ADMIN_EXPIRES_IN=12h
ACCESS_TOKEN_SECRET=mengtutv_access_token_secret_development_2024
REFRESH_TOKEN_SECRET=mengtutv_refresh_token_secret_development_2024

# CORS配置 - 本地开发域名
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,http://127.0.0.1:3000,http://127.0.0.1:5173

# 文件上传配置 - 本地路径
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=52428800
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp,application/pdf,video/mp4,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document

# 日志配置 - 本地路径
LOG_LEVEL=debug
LOG_DIR=./logs

# 应用配置 - 本地域名
APP_URL=http://localhost:3000
API_URL=http://localhost:3001/api

# 云存储配置 - 开发环境
OSS_PROVIDER=cos
COS_SECRET_ID=your_cos_secret_id_dev
COS_SECRET_KEY=your_cos_secret_key_dev
COS_REGION=ap-shanghai
COS_BUCKET=mengtu-development
COS_DIRECTORY=mengtutv-dev
