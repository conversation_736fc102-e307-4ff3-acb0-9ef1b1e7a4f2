@echo off
echo 🚀 启动开发环境...

echo.
echo 📋 启动步骤:
echo 1. 切换到开发环境配置
echo 2. 启动后端服务 (端口: 3001)
echo 3. 启动管理后台 (端口: 5173)
echo 4. 启动官网 (端口: 3000)

echo.
echo 🔄 切换环境配置...
node scripts/switch-env.js development

echo.
echo 🔧 请手动执行以下命令启动各个服务:
echo.
echo 后端服务:
echo   cd backend
echo   npm run dev
echo.
echo 管理后台:
echo   cd fundAdmin
echo   npm run dev
echo.
echo 官网:
echo   cd website  
echo   npm run dev

pause
