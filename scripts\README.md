# 环境切换工具使用说明

## 概述

本工具用于在开发环境和生产环境之间快速切换配置，支持 Backend、FundAdmin、Website 三个项目的统一环境管理。

## 文件结构

```
scripts/
├── switch-env.js      # 主要的环境切换脚本
├── dev-env.bat        # Windows: 切换到开发环境
├── prod-env.bat       # Windows: 切换到生产环境  
├── check-env.bat      # Windows: 检查当前环境
├── dev-env.sh         # Linux/Mac: 切换到开发环境
├── prod-env.sh        # Linux/Mac: 切换到生产环境
├── start-dev.bat      # Windows: 启动开发环境
└── README.md          # 本说明文档
```

## 使用方法

### 1. 快速切换（推荐）

**Windows:**
```bash
# 切换到开发环境
scripts/dev-env.bat

# 切换到生产环境  
scripts/prod-env.bat

# 检查当前环境
scripts/check-env.bat
```

**Linux/Mac:**
```bash
# 给脚本执行权限
chmod +x scripts/*.sh

# 切换到开发环境
./scripts/dev-env.sh

# 切换到生产环境
./scripts/prod-env.sh
```

### 2. 手动切换

```bash
# 切换到开发环境
node scripts/switch-env.js development

# 切换到生产环境
node scripts/switch-env.js production

# 查看当前环境状态
node scripts/switch-env.js status

# 显示帮助信息
node scripts/switch-env.js help
```

## 环境配置说明

### 开发环境配置

- **Backend**: 使用本地数据库 (127.0.0.1:3306)
- **API地址**: http://localhost:3001/api
- **CORS**: 允许本地前端访问
- **日志级别**: debug
- **文件上传**: 本地目录

### 生产环境配置

- **Backend**: 使用生产数据库
- **API地址**: https://api.qinghee.com.cn/api  
- **CORS**: 仅允许生产域名
- **日志级别**: warn
- **文件上传**: 服务器目录

## 启动开发环境

1. 切换到开发环境：
   ```bash
   scripts/dev-env.bat  # Windows
   ./scripts/dev-env.sh # Linux/Mac
   ```

2. 启动各个服务：
   ```bash
   # 后端服务 (端口: 3001)
   cd backend
   npm run dev

   # 管理后台 (端口: 5173)  
   cd fundAdmin
   npm run dev

   # 官网 (端口: 3000)
   cd website
   npm run dev
   ```

## 注意事项

1. **数据库配置**: 开发环境需要本地MySQL数据库
2. **端口冲突**: 确保相关端口未被占用
3. **环境变量**: 切换后需要重启服务才能生效
4. **云存储**: 开发环境建议使用独立的COS bucket

## 故障排除

### 1. 脚本执行失败
- 确保Node.js已安装
- 检查文件路径是否正确
- 确认有文件读写权限

### 2. 环境切换后服务启动失败
- 检查数据库连接配置
- 确认端口是否被占用
- 查看日志文件排查错误

### 3. API请求失败
- 确认后端服务已启动
- 检查CORS配置
- 验证API地址是否正确

## 自定义配置

如需修改环境配置，请编辑对应的环境文件：

- `backend/.env.development` - 后端开发环境
- `backend/.env.production` - 后端生产环境  
- `fundAdmin/.env.development` - 管理后台开发环境
- `fundAdmin/.env.production` - 管理后台生产环境
- `website/.env.development` - 官网开发环境
- `website/.env.production` - 官网生产环境
