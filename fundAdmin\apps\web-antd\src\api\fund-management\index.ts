import { requestClient } from '#/api/request';
import type { FundManagementApi } from './types';

/**
 * 获取基金列表
 */
export async function getFundList(params: FundManagementApi.FundListParams = {}) {
  return requestClient.get<FundManagementApi.FundListResponse>('/admin/funds', {
    params,
  });
}

/**
 * 获取基金详情
 */
export async function getFundDetail(id: number) {
  return requestClient.get<FundManagementApi.FundDetailResponse>(`/admin/funds/${id}`);
}

/**
 * 创建基金
 */
export async function createFund(data: FundManagementApi.CreateFundParams) {
  return requestClient.post<FundManagementApi.OperationResponse>('/admin/funds', data);
}

/**
 * 更新基金信息
 */
export async function updateFund(id: number, data: FundManagementApi.UpdateFundParams) {
  return requestClient.put<FundManagementApi.OperationResponse>(`/admin/funds/${id}`, data);
}

/**
 * 删除基金
 */
export async function deleteFund(id: number) {
  return requestClient.delete<FundManagementApi.OperationResponse>(`/admin/funds/${id}`);
}

/**
 * 批量删除基金
 */
export async function batchDeleteFunds(ids: number[]) {
  return requestClient.post<FundManagementApi.OperationResponse>('/admin/funds/batch-delete', {
    ids,
  });
}

/**
 * 获取基金统计信息
 */
export async function getFundStats() {
  return requestClient.get<FundManagementApi.FundStatsResponse>('/admin/funds/stats');
}

/**
 * 更新基金发布状态
 */
export async function updateFundStatus(id: number, is_published: number) {
  return requestClient.put<FundManagementApi.OperationResponse>(`/admin/funds/${id}`, {
    is_published,
  });
}

// ==================== 基金子表数据 CRUD 接口 ====================

/**
 * 基金亮点相关接口
 */
export async function createFundHighlight(fundId: number, data: { content: string; sortOrder: number }) {
  return requestClient.post<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/highlights`, data);
}

export async function updateFundHighlight(fundId: number, highlightId: number, data: { content: string; sortOrder: number }) {
  return requestClient.put<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/highlights/${highlightId}`, data);
}

export async function deleteFundHighlight(fundId: number, highlightId: number) {
  return requestClient.delete<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/highlights/${highlightId}`);
}

/**
 * 基金文档相关接口
 */
export async function createFundDocument(fundId: number, data: { name: string; fileUrl: string; fileType?: string; fileSize?: string }) {
  return requestClient.post<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/documents`, data);
}

export async function updateFundDocument(fundId: number, documentId: number, data: { name: string; fileUrl: string; fileType?: string; fileSize?: string }) {
  return requestClient.put<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/documents/${documentId}`, data);
}

export async function deleteFundDocument(fundId: number, documentId: number) {
  return requestClient.delete<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/documents/${documentId}`);
}

export async function uploadFundDocument(fundId: number, file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return requestClient.post<{
    success: boolean;
    message: string;
    data: {
      name: string;
      fileUrl: string;
      fileType: string;
      fileSize: string;
      originalName: string;
      filename: string;
      size: number;
    };
  }>(`/admin/funds/${fundId}/documents/upload`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    // 文件上传需要更长的超时时间，设置为60秒
    timeout: 60000,
  });
}

/**
 * 基金FAQ相关接口
 */
export async function createFundFaq(fundId: number, data: { question: string; answer: string; sortOrder: number }) {
  return requestClient.post<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/faqs`, data);
}

export async function updateFundFaq(fundId: number, faqId: number, data: { question: string; answer: string; sortOrder: number }) {
  return requestClient.put<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/faqs/${faqId}`, data);
}

export async function deleteFundFaq(fundId: number, faqId: number) {
  return requestClient.delete<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/faqs/${faqId}`);
}

/**
 * 基金时间线相关接口
 */
export async function createFundTimeline(fundId: number, data: {
  stage: string;
  startDate: string;
  endDate?: string;
  isOpenEnded?: boolean;
  status: string;
  sortOrder: number
}) {
  return requestClient.post<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/timelines`, data);
}

export async function updateFundTimeline(fundId: number, timelineId: number, data: {
  stage: string;
  startDate: string;
  endDate?: string;
  isOpenEnded?: boolean;
  status: string;
  sortOrder: number
}) {
  return requestClient.put<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/timelines/${timelineId}`, data);
}

export async function deleteFundTimeline(fundId: number, timelineId: number) {
  return requestClient.delete<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/timelines/${timelineId}`);
}

/**
 * 基金费用结构相关接口
 */
export async function createFundFee(fundId: number, data: { name: string; value: string; sortOrder: number }) {
  return requestClient.post<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/fees`, data);
}

export async function updateFundFee(fundId: number, feeId: number, data: { name: string; value: string; sortOrder: number }) {
  return requestClient.put<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/fees/${feeId}`, data);
}

export async function deleteFundFee(fundId: number, feeId: number) {
  return requestClient.delete<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/fees/${feeId}`);
}

/**
 * 基金历史业绩相关接口
 */
export async function createFundPerformance(fundId: number, data: { year: string; return: string; average?: string }) {
  return requestClient.post<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/performances`, data);
}

export async function updateFundPerformance(fundId: number, performanceId: number, data: { year: string; return: string; average?: string }) {
  return requestClient.put<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/performances/${performanceId}`, data);
}

export async function deleteFundPerformance(fundId: number, performanceId: number) {
  return requestClient.delete<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/performances/${performanceId}`);
}

/**
 * 基金资金使用计划相关接口
 */
export async function createFundUsagePlan(fundId: number, data: { purpose: string; amount: string; percentage: string; sortOrder: number }) {
  return requestClient.post<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/usage-plans`, data);
}

export async function updateFundUsagePlan(fundId: number, planId: number, data: { purpose: string; amount: string; percentage: string; sortOrder: number }) {
  return requestClient.put<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/usage-plans/${planId}`, data);
}

export async function deleteFundUsagePlan(fundId: number, planId: number) {
  return requestClient.delete<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/usage-plans/${planId}`);
}

/**
 * 基金成功案例相关接口
 */
export async function createFundSuccessCase(fundId: number, data: { title: string; description: string; return: string; investment?: string; sortOrder: number }) {
  return requestClient.post<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/success-cases`, data);
}

export async function updateFundSuccessCase(fundId: number, caseId: number, data: { title: string; description: string; return: string; investment?: string; sortOrder: number }) {
  return requestClient.put<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/success-cases/${caseId}`, data);
}

export async function deleteFundSuccessCase(fundId: number, caseId: number) {
  return requestClient.delete<FundManagementApi.OperationResponse>(`/admin/funds/${fundId}/success-cases/${caseId}`);
}

// 导出类型
export type { FundManagementApi };
