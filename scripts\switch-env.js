#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 项目配置
const projects = {
  backend: {
    name: 'Backend',
    path: './backend',
    envFiles: {
      development: '.env.development',
      production: '.env.production'
    },
    targetFile: '.env'
  },
  fundAdmin: {
    name: 'FundAdmin',
    path: './fundAdmin',
    envFiles: {
      development: '.env.development',
      production: '.env.production'
    },
    targetFile: '.env'
  },
  website: {
    name: 'Website',
    path: './website',
    envFiles: {
      development: '.env.development',
      production: '.env.production'
    },
    targetFile: '.env'
  }
};

function copyEnvFile(project, environment) {
  const projectConfig = projects[project];
  const sourcePath = path.join(projectConfig.path, projectConfig.envFiles[environment]);
  const targetPath = path.join(projectConfig.path, projectConfig.targetFile);

  try {
    if (!fs.existsSync(sourcePath)) {
      log(`❌ 源文件不存在: ${sourcePath}`, 'red');
      return false;
    }

    fs.copyFileSync(sourcePath, targetPath);
    log(`✅ ${projectConfig.name}: ${environment} 环境配置已生效`, 'green');
    return true;
  } catch (error) {
    log(`❌ ${projectConfig.name}: 复制失败 - ${error.message}`, 'red');
    return false;
  }
}

function switchEnvironment(environment) {
  if (!['development', 'production'].includes(environment)) {
    log('❌ 无效的环境参数，请使用 development 或 production', 'red');
    process.exit(1);
  }

  log(`\n🔄 正在切换到 ${environment} 环境...`, 'cyan');
  log('='.repeat(50), 'blue');

  let successCount = 0;
  const totalProjects = Object.keys(projects).length;

  for (const [projectKey, projectConfig] of Object.entries(projects)) {
    if (copyEnvFile(projectKey, environment)) {
      successCount++;
    }
  }

  log('='.repeat(50), 'blue');
  
  if (successCount === totalProjects) {
    log(`🎉 所有项目已成功切换到 ${environment} 环境！`, 'green');
    log('\n📋 下一步操作:', 'yellow');
    log('1. 重启所有开发服务器', 'yellow');
    log('2. 检查数据库连接配置', 'yellow');
    log('3. 验证API接口地址', 'yellow');
  } else {
    log(`⚠️  部分项目切换失败 (${successCount}/${totalProjects})`, 'yellow');
  }
}

function showCurrentEnvironment() {
  log('\n📊 当前环境状态:', 'cyan');
  log('='.repeat(50), 'blue');

  for (const [projectKey, projectConfig] of Object.entries(projects)) {
    const envPath = path.join(projectConfig.path, projectConfig.targetFile);
    
    if (fs.existsSync(envPath)) {
      const content = fs.readFileSync(envPath, 'utf8');
      const isProduction = content.includes('NODE_ENV=production') || 
                          content.includes('生产环境') ||
                          content.includes('production');
      const env = isProduction ? 'production' : 'development';
      const color = isProduction ? 'red' : 'green';
      log(`${projectConfig.name}: ${env}`, color);
    } else {
      log(`${projectConfig.name}: 未配置`, 'yellow');
    }
  }
}

function showHelp() {
  log('\n🛠️  环境切换工具', 'cyan');
  log('='.repeat(50), 'blue');
  log('用法:', 'yellow');
  log('  node scripts/switch-env.js <environment>', 'white');
  log('  node scripts/switch-env.js status', 'white');
  log('  node scripts/switch-env.js help', 'white');
  log('\n参数:', 'yellow');
  log('  development  - 切换到开发环境', 'white');
  log('  production   - 切换到生产环境', 'white');
  log('  status       - 显示当前环境状态', 'white');
  log('  help         - 显示帮助信息', 'white');
  log('\n示例:', 'yellow');
  log('  node scripts/switch-env.js development', 'white');
  log('  node scripts/switch-env.js production', 'white');
}

// 主程序
const args = process.argv.slice(2);
const command = args[0];

if (!command || command === 'help') {
  showHelp();
} else if (command === 'status') {
  showCurrentEnvironment();
} else {
  switchEnvironment(command);
}
