# 🔄 切换到开发环境（测试你的功能）
node scripts/switch-env.js development
# 或者直接双击：scripts/dev-env.bat

# 🔄 切换到生产环境
node scripts/switch-env.js production  
# 或者直接双击：scripts/prod-env.bat

# 📊 查看当前环境状态
node scripts/switch-env.js status
# 或者直接双击：scripts/check-env.bat

backend 后端服务部署流程

cd backend
# 安装生产依赖
pnpm install --production

# 方法1：直接设置环境变量构建
NODE_ENV=production pnpm run build
# 或者在Windows PowerShell中：
$env:NODE_ENV="production"; pnpm run build
# 或者使用cross-env（如果已安装）
npx cross-env NODE_ENV=production pnpm run build

# 方法2：在服务器上操作：
cd /www/wwwroot/api.qinghee.com.cn

pnpm install --prod

pm2 start ecosystem.config.js

sleep 5

pm2 status

pm2 logs fundAdmin-backend --lines 20

https://api.qinghee.com.cn/api/health 检测生产环境接口是否接通


-----------打包fundAdmin---------------------
pnpm build:antd