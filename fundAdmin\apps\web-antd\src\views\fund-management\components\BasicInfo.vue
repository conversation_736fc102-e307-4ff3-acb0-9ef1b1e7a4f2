<template>
  <div class="admin-card">
    <a-form
      :model="formState"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
      class="settings-form"
      layout="horizontal"
    >
      <!-- 基金基本信息 -->
      <a-form-item label="基金代码" class="form-item-visible">
        <a-input
          :value="fund.code"
          disabled
          class="input-disabled"
        />
      </a-form-item>

      <a-form-item label="基金名称" class="form-item-visible">
        <a-input
          :value="fund.title"
          disabled
          class="input-disabled"
        />
      </a-form-item>

      <a-form-item label="基金描述" class="form-item-visible">
        <a-textarea
          :value="fund.description"
          :rows="3"
          disabled
          class="textarea-disabled"
        />
      </a-form-item>

      <a-form-item label="基金类型" class="form-item-visible">
        <a-input
          :value="fundTypeLabels[fund.type]"
          disabled
          class="input-disabled"
        />
      </a-form-item>

      <a-form-item label="风险等级" class="form-item-visible">
        <a-input
          :value="fund.risk"
          disabled
          class="input-disabled"
        />
      </a-form-item>

      <a-form-item label="最低起投金额" class="form-item-visible">
        <a-input
          :value="formatCurrency(fund.min_investment)"
          disabled
          class="input-disabled"
        />
      </a-form-item>

      <a-form-item label="封闭期类型" class="form-item-visible">
        <a-input
          :value="periodLabels[fund.period]"
          disabled
          class="input-disabled"
        />
      </a-form-item>

      <a-form-item label="目标募集规模" class="form-item-visible">
        <a-input
          :value="formatCurrency(fund.target_size)"
          disabled
          class="input-disabled"
        />
      </a-form-item>

      <a-form-item label="已募集金额" class="form-item-visible">
        <a-input
          :value="formatCurrency(fund.raised_amount)"
          disabled
          class="input-disabled"
        />
      </a-form-item>

      <a-form-item label="募集进度" class="form-item-visible">
        <div class="flex items-center space-x-4">
          <a-progress
            :percent="calculateProgress(fund.raised_amount, fund.target_size)"
            :show-info="false"
            class="flex-1"
          />
          <span class="text-sm font-medium">{{ calculateProgress(fund.raised_amount, fund.target_size) }}%</span>
        </div>
      </a-form-item>

      <a-form-item label="预期年化收益率" class="form-item-visible">
        <a-input
          :value="fund.expected_return || '-'"
          disabled
          class="input-disabled"
        />
      </a-form-item>

      <a-form-item label="最低持有期" class="form-item-visible">
        <a-input
          :value="`${fund.min_holding_period}个月`"
          disabled
          class="input-disabled"
        />
      </a-form-item>

      <a-form-item label="基金管理人" class="form-item-visible">
        <a-input
          :value="fund.manager || '-'"
          disabled
          class="input-disabled"
        />
      </a-form-item>

      <a-form-item label="基金托管人" class="form-item-visible">
        <a-input
          :value="fund.trustee || '-'"
          disabled
          class="input-disabled"
        />
      </a-form-item>

      <a-form-item label="成立日期" class="form-item-visible">
        <a-input
          :value="formatDate(fund.establish_date)"
          disabled
          class="input-disabled"
        />
      </a-form-item>

      <a-form-item label="到期日期" class="form-item-visible">
        <a-input
          :value="formatDate(fund.exit_date)"
          disabled
          class="input-disabled"
        />
      </a-form-item>

      <a-form-item label="投资策略" class="form-item-visible">
        <a-textarea
          :value="fund.investment_strategy || '-'"
          :rows="3"
          disabled
          class="textarea-disabled"
        />
      </a-form-item>

      <a-form-item label="赎回政策" class="form-item-visible">
        <a-textarea
          :value="fund.redemption_policy || '-'"
          :rows="3"
          disabled
          class="textarea-disabled"
        />
      </a-form-item>

      <a-form-item label="风险描述" class="form-item-visible">
        <a-textarea
          :value="fund.risk_description || '-'"
          :rows="3"
          disabled
          class="textarea-disabled"
        />
      </a-form-item>

      <a-form-item label="创建时间" class="form-item-visible">
        <a-input
          :value="formatDate(fund.created_at)"
          disabled
          class="input-disabled"
        />
      </a-form-item>

      <a-form-item label="更新时间" class="form-item-visible">
        <a-input
          :value="formatDate(fund.updated_at)"
          disabled
          class="input-disabled"
        />
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { Form as AForm, FormItem as AFormItem, Input as AInput, Textarea as ATextarea, Progress as AProgress } from 'ant-design-vue';

interface Props {
  fund: any;
}

const props = defineProps<Props>();

// 表单状态（虽然是只读，但保持结构一致）
const formState = ref({});

// 基金类型标签
const fundTypeLabels = {
  equity: '股权型',
  debt: '债权型',
  mixed: '混合型',
};

// 封闭期类型标签
const periodLabels = {
  short: '≤1年',
  medium: '1-3年',
  long: '>3年',
};

// 格式化货币
const formatCurrency = (amount: number) => {
  if (!amount) return '0';
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

// 计算进度百分比
const calculateProgress = (raised: number, target: number) => {
  if (!target) return 0;
  return Math.round((raised / target) * 100);
};

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-';
  return new Date(dateStr).toLocaleDateString('zh-CN');
};
</script>

<style scoped>
.admin-card {
  @apply bg-white p-6 rounded-lg shadow-sm;
}

.settings-form {
  @apply max-w-none;
}

.form-item-visible {
  @apply mb-4;
}

.input-disabled,
.textarea-disabled {
  @apply bg-gray-50 text-gray-700;
}

.input-disabled:disabled,
.textarea-disabled:disabled {
  @apply bg-gray-50 text-gray-700 cursor-default;
}

:deep(.ant-form-item-label) {
  @apply font-medium;
}

:deep(.ant-progress-bg) {
  @apply bg-primary;
}
</style>
