/**
 * 验证工具函数
 */

/**
 * 验证邮箱格式
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 验证手机号格式（中国大陆）
 */
export function validatePhone(phone: string): boolean {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
}

/**
 * 验证密码强度
 */
export function validatePassword(password: string): { valid: boolean; message: string } {
  if (password.length < 8) {
    return { valid: false, message: '密码长度至少8位' };
  }
  
  if (!/[a-z]/.test(password)) {
    return { valid: false, message: '密码必须包含小写字母' };
  }
  
  if (!/[A-Z]/.test(password)) {
    return { valid: false, message: '密码必须包含大写字母' };
  }
  
  if (!/\d/.test(password)) {
    return { valid: false, message: '密码必须包含数字' };
  }
  
  return { valid: true, message: '密码强度符合要求' };
}

/**
 * 验证用户名格式
 */
export function validateUsername(username: string): { valid: boolean; message: string } {
  if (!username || username.length < 3) {
    return { valid: false, message: '用户名长度至少3位' };
  }
  
  if (username.length > 50) {
    return { valid: false, message: '用户名长度不能超过50位' };
  }
  
  // 只允许字母、数字、下划线
  if (!/^[a-zA-Z0-9_]+$/.test(username)) {
    return { valid: false, message: '用户名只能包含字母、数字和下划线' };
  }
  
  return { valid: true, message: '用户名格式正确' };
}

/**
 * 验证基金数据
 */
export function validateFundData(data: any, isUpdate = false): { isValid: boolean; errors: Record<string, string> } {
  const errors: Record<string, string> = {};
  
  // 如果是新建基金，这些字段是必须的
  if (!isUpdate) {
    if (!data.code) errors.code = '基金代码是必须的';
    if (!data.title) errors.title = '基金名称是必须的';
    if (!data.type) errors.type = '基金类型是必须的';
    if (!data.risk) errors.risk = '风险等级是必须的';
    if (!data.min_investment) errors.min_investment = '最低投资金额是必须的';
    if (!data.period) errors.period = '封闭期是必须的';
    if (!data.target_size) errors.target_size = '目标规模是必须的';
    if (!data.expected_return) errors.expected_return = '预期收益率是必须的';
    if (!data.min_holding_period) errors.min_holding_period = '最低持有期是必须的';
  }
  
  // 如果提供了下列字段，则进行验证
  if (data.code !== undefined && (!data.code || data.code.length > 32)) {
    errors.code = '基金代码不能为空且长度不能超过32个字符';
  }
  
  if (data.title !== undefined && (!data.title || data.title.length > 100)) {
    errors.title = '基金名称不能为空且长度不能超过100个字符';
  }
  
  if (data.type !== undefined && !['equity', 'debt', 'mixed'].includes(data.type)) {
    errors.type = '基金类型必须是equity、debt或mixed之一';
  }
  
  if (data.risk !== undefined && !['R1', 'R2', 'R3', 'R4', 'R5'].includes(data.risk)) {
    errors.risk = '风险等级必须是R1、R2、R3、R4或R5之一';
  }
  
  if (data.period !== undefined && !['short', 'medium', 'long'].includes(data.period)) {
    errors.period = '封闭期必须是short、medium或long之一';
  }
  
  if (data.min_investment !== undefined) {
    const minInvestment = parseInt(data.min_investment);
    if (isNaN(minInvestment) || minInvestment <= 0) {
      errors.min_investment = '最低投资金额必须是正整数';
    }
  }

  if (data.target_size !== undefined) {
    const targetSize = parseInt(data.target_size);
    if (isNaN(targetSize) || targetSize <= 0) {
      errors.target_size = '目标规模必须是正整数';
    }
  }

  if (data.expected_return !== undefined) {
    const expectedReturn = parseFloat(data.expected_return);
    if (isNaN(expectedReturn) || expectedReturn < 0 || expectedReturn > 100) {
      errors.expected_return = '预期收益率必须是0-100之间的数字';
    }
  }

  if (data.min_holding_period !== undefined) {
    const minHoldingPeriod = parseInt(data.min_holding_period);
    if (isNaN(minHoldingPeriod) || minHoldingPeriod <= 0) {
      errors.min_holding_period = '最低持有期必须是正整数';
    }
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

/**
 * 验证分页参数
 */
export function validatePagination(page?: string | number, pageSize?: string | number): {
  page: number;
  pageSize: number;
  offset: number;
} {
  const validPage = Math.max(1, parseInt(String(page)) || 1);
  const validPageSize = Math.min(100, Math.max(1, parseInt(String(pageSize)) || 10));
  const offset = (validPage - 1) * validPageSize;
  
  return {
    page: validPage,
    pageSize: validPageSize,
    offset
  };
}

/**
 * 验证ID参数
 */
export function validateId(id: string | number): number | null {
  const numId = parseInt(String(id));
  return isNaN(numId) || numId <= 0 ? null : numId;
}

/**
 * 验证状态值
 */
export function validateStatus(status: string | number): 0 | 1 | null {
  const numStatus = parseInt(String(status));
  return [0, 1].includes(numStatus) ? numStatus as 0 | 1 : null;
}
